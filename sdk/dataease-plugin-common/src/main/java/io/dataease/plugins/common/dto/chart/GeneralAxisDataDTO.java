package io.dataease.plugins.common.dto.chart;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GeneralAxisDataDTO {

    private String id;                 // 字段唯一标识

    private String tableId;           // 所属表ID

    private String originName;        // 原始字段名（如数据库中的列名）

    private String name;              // 字段中文名称

    private String dataeaseName;      // 在 DataEase 中的字段名

    private String groupType;         // 分组类型，如维度 d

    private String type;              // 数据类型（如 VARCHAR2）

    private Integer size;             // 字段长度

    private Integer deType;           // 数据元素类型

    private String deTypeFormat;      // 数据元素格式

    private Integer deExtractType;    // 数据提取方式

    private Integer extField;         // 是否扩展字段

    private Boolean checked;          // 是否勾选/启用

    private Integer columnIndex;      // 字段在表中的列索引

    private Long lastSyncTime;        // 最近同步时间（时间戳）

    private Integer accuracy;         // 精度（小数点位数）

    private String dateFormat;        // 日期格式

    private String dateFormatType;    // 日期格式类型

    private String sort = "none";              // 排序方式

    private FormatterCfg formatterCfg = new FormatterCfg(); // 格式化配置

    private String dateStyle = "";         // 日期风格（如 y_M_d）

    private String datePattern = "date_sub";       // 日期模式（如 date_sub）

    private List<Object> filter = new ArrayList<>();      // 过滤器列表（具体类型视业务而定）

    private ChartFieldCompareDTO compareCalc;

    private boolean compareCalc;

    // 内部类：格式化配置
    @Data
    public static class FormatterCfg {
        private String type = "auto";
        private Integer unit = 1;
        private String suffix = "";
        private Integer decimalCount = 2;
        private Boolean thousandSeparator = true;
    }
}
