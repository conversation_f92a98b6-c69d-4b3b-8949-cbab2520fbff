package io.dataease.service.chart;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import io.dataease.commons.constants.AuthConstants;
import io.dataease.commons.constants.CommonConstants;
import io.dataease.commons.constants.SysAuthConstants;
import io.dataease.commons.utils.AuthUtils;
import io.dataease.commons.utils.BeanUtils;
import io.dataease.controller.request.dataset.DataSetTableRequest;
import io.dataease.controller.request.panel.PanelGroupRequest;
import io.dataease.listener.util.CacheUtils;
import io.dataease.plugins.common.base.domain.ChartView;
import io.dataease.plugins.common.base.domain.ChartViewExample;
import io.dataease.plugins.common.base.domain.ChartViewWithBLOBs;
import io.dataease.plugins.common.base.domain.DatasetGroup;
import io.dataease.plugins.common.base.domain.DatasetGroupExample;
import io.dataease.plugins.common.base.domain.DatasetTable;
import io.dataease.plugins.common.base.domain.DatasetTableExample;
import io.dataease.plugins.common.base.domain.DatasetTableField;
import io.dataease.plugins.common.base.domain.DatasetTableFieldExample;
import io.dataease.plugins.common.base.domain.Datasource;
import io.dataease.plugins.common.base.domain.PanelGroupExample;
import io.dataease.plugins.common.base.domain.PanelGroupWithBLOBs;
import io.dataease.plugins.common.base.domain.PanelView;
import io.dataease.plugins.common.base.domain.PanelViewExample;
import io.dataease.plugins.common.base.mapper.ChartViewMapper;
import io.dataease.plugins.common.base.mapper.DatasetGroupMapper;
import io.dataease.plugins.common.base.mapper.DatasetTableFieldMapper;
import io.dataease.plugins.common.base.mapper.DatasetTableMapper;
import io.dataease.plugins.common.base.mapper.DatasourceMapper;
import io.dataease.plugins.common.base.mapper.PanelGroupMapper;
import io.dataease.plugins.common.base.mapper.PanelViewMapper;
import io.dataease.plugins.common.constants.top3Index.Top3IndexTemplateEnum;
import io.dataease.plugins.common.dto.chart.ChartViewFieldDTO;
import io.dataease.plugins.common.dto.chart.GeneralAxisDataDTO;
import io.dataease.plugins.common.dto.top3Index.Top3IndexDataSetInfoDTO;
import io.dataease.plugins.common.dto.top3Index.Top3IndexInfoDTO;
import io.dataease.service.dataset.DataSetTableService;
import io.dataease.service.panel.PanelGroupService;
import io.dataease.service.sys.SysAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

/**
 * 医院等级评审系统指标生成
 */
@Slf4j
@Service
public class Top3IndexGenerateService {

    /**
     * 三甲-比率模板ID
     */
    @Value("${top3index.rateTemplateId}")
    String rateTemplateId;

    /**
     * 三甲-单病种模板ID
     */
    @Value("${top3index.singleDiseaseTemplateId}")
    String singleDiseaseTemplateId;

    /**
     * 三甲-数据源
     */
    @Value("${top3index.datasourceId}")
    String datasourceId;

    @Resource
    DatasourceMapper datasourceMapper;

    @Resource
    PanelGroupMapper panelGroupMapper;

    @Resource
    SysAuthService sysAuthService;

    @Resource
    DatasetTableMapper datasetTableMapper;

    @Resource
    DatasetGroupMapper datasetGroupMapper;

    @Resource
    PanelViewMapper panelViewMapper;

    @Resource
    ChartViewMapper chartViewMapper;

    @Resource
    DataSetTableService dataSetTableService;

    @Resource
    PanelGroupService panelGroupService;

    @Resource
    ChartViewFieldService chartViewFieldService;

    @Resource
    ChartViewService chartViewService;

    @Resource
    DatasetTableFieldMapper datasetTableFieldMapper;

    @Resource
    private Gson gson;

    /**
     * 构建仪表板中的LABEL内容
     * @param panelId 仪表板id
     * @param task 指标信息
     */
    private void buildPanelLabel(String panelId,Top3IndexInfoDTO task) {
        //指标分子
        String numerator = StringUtils.EMPTY;
        //指标分母
        String denominator = StringUtils.EMPTY;
        //指标数据集列表
        List<Top3IndexDataSetInfoDTO> dataSetList = task.getDataSetList();
        //数据过滤
        Optional<Top3IndexDataSetInfoDTO> numeratorOpt = dataSetList.stream().filter(item -> item.getDatasetName().contains("分子-")).findFirst();
        if(numeratorOpt.isPresent()){
            numerator = numeratorOpt.get().getDatasetName().replace("分子-","");
        }
        Optional<Top3IndexDataSetInfoDTO> denominatorOpt = dataSetList.stream().filter(item -> item.getDatasetName().contains("分母-")).findFirst();
        if(denominatorOpt.isPresent()){
            denominator = denominatorOpt.get().getDatasetName().replace("分母-","");
        }

        PanelGroupWithBLOBs panelGroup= panelGroupMapper.selectByPrimaryKey(panelId);
        PanelGroupExample panelGroupExample = new PanelGroupExample();
        panelGroupExample.createCriteria().andIdEqualTo(panelId);
        panelGroup.setPanelData(panelGroup.getPanelData()
                .replace("LABEL_比率",task.getIndexName())
                .replace("LABEL_分子总数",numerator)
                .replace("LABEL_分母总数",denominator)
                .replace("LABEL_分子",numerator)
                .replace("LABEL_分母",denominator)
                .replace("LABEL_分子明细",numerator + "明细")
                .replace("LABEL_分母明细",denominator + "明细")
        );
        panelGroupMapper.updateByExampleWithBLOBs(panelGroup,panelGroupExample);
    }

    public void top3IndexGenerate(Top3IndexInfoDTO task,Datasource datasource){
        try {
            //查询/构建数据集分组信息
            DatasetGroup datasetIndexInfo = getDatasetIndexInfo(task);
            //生成三甲指标数据集
            List<DatasetTable> datasetTableList = saveIndexDataset(datasource.getId(), datasetIndexInfo, task);
            //生成对应的仪表板分组
            PanelGroupWithBLOBs panelGroupInfo = buildPanelGroupInfo(task);
            //复制模板仪表板
            String panelId = copyTemplatePanel(task, panelGroupInfo);
            //构建仪表板中的LABEL内容 - 分子分母比率模板
            if(Top3IndexTemplateEnum.RATE.getCode().equals(task.getIndexType())) {
                buildPanelLabel(panelId, task);
            }
            //调整仪表板视图的数据集及展示字段
            adjustDatasetAndField(task.getIndexType(),panelId, datasetTableList);
            //关联查询条件
            associatedQueryCondition(panelGroupInfo, datasetTableList);
        }catch (Exception e){
            log.info("三甲指标生成异常,指标信息:{},异常原因:{}", JSONObject.toJSONString(task),e.getMessage());
        }
    }

    /**
     * 绑定仪表板过滤组件
     * @param panelGroupInfo 仪表板信息
     * @param datasetTableList 数据集集合
     */
    private void associatedQueryCondition(PanelGroupWithBLOBs panelGroupInfo,List<DatasetTable> datasetTableList) {
        //TODO 数据集不定无法确定按什么字段关联
    }

    /**
     * 调整仪表板中视图的数据集和字段
     * @param templateType 模板类型 1-分子分母比率模板 2-单病种模板
     * @param panelId 仪表板ID
     * @param datasetTables 生成的数据集
     */
    private void adjustDatasetAndField(Integer templateType,String panelId,List<DatasetTable> datasetTables) {
        //获取仪表盘下所有的视图
        PanelViewExample viewExample = new PanelViewExample();
        viewExample.createCriteria().andPanelIdEqualTo(panelId);
        List<PanelView> panelViews = panelViewMapper.selectByExample(viewExample);
        List<String> chartViewIdList = panelViews.stream().map(PanelView::getChartViewId).collect(Collectors.toList());
        //删除视图的字段
        chartViewFieldService.deleteByChartIds(chartViewIdList);
        //查询视图的信息
        ChartViewExample chartViewExample = new ChartViewExample();
        chartViewExample.createCriteria().andIdIn(chartViewIdList);
        List<ChartViewWithBLOBs> chartViews = chartViewMapper.selectByExampleWithBLOBs(chartViewExample);
        chartViews.forEach(chartView -> {
            ChartViewWithBLOBs chartViewEdit = new ChartViewWithBLOBs();
            //构建编辑视图信息入参
            if(Top3IndexTemplateEnum.RATE.getCode().equals(templateType)) {
                //1.分子分母比率模板
                chartViewEdit = buildRateChartViewEditParam(chartView, datasetTables);
            }else if(Top3IndexTemplateEnum.SINGLE_DISEASE.getCode().equals(templateType)){
                //2.单病种模板
                chartViewEdit = buildSingleDiseaseChartViewEditParam(chartView, datasetTables);
            }
            //编辑视图
            chartViewService.viewEditSave(chartViewEdit);
        });
    }

    //构建编辑视图信息入参 - 分子分母比率模板
    private ChartViewWithBLOBs buildRateChartViewEditParam(ChartView chartView, List<DatasetTable> datasetTables) {
        ChartViewWithBLOBs chartViewWithBLOBs = new ChartViewWithBLOBs();
        BeanUtils.copyBean(chartViewWithBLOBs,chartView);
        chartViewWithBLOBs.setCustomFilter("{}");
        chartViewWithBLOBs.setDataFrom(CommonConstants.VIEW_DATA_FROM.CHART);
        chartViewWithBLOBs.setDrillFields("[]");
        chartViewWithBLOBs.setExtBubble("[]");
        chartViewWithBLOBs.setExtStack("[]");
        if(chartView.getName().contains("分子总数")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> fzDatasetOpt = datasetTables.stream().filter(item -> item.getName().contains("分子")).findFirst();
            if(fzDatasetOpt.isPresent()){
                String tableId = fzDatasetOpt.get().getId();
                //设置分子总数
                chartViewWithBLOBs.setYAxis(buildCountAxis(tableId));
                chartViewWithBLOBs.setTableId(tableId);
            }
            chartViewWithBLOBs.setViewFields("[]");
            chartViewWithBLOBs.setXAxis("[]");
            chartViewWithBLOBs.setXAxisExt("[]");
            chartViewWithBLOBs.setYAxisExt("[]");
            chartViewWithBLOBs.setSenior("{\"functionCfg\":{\"sliderShow\":false,\"sliderRange\":[0,10],\"sliderBg\":\"#FFFFFF\",\"sliderFillBg\":\"#BCD6F1\",\"sliderTextClolor\":\"#999999\",\"emptyDataStrategy\":\"breakLine\",\"emptyDataFieldCtrl\":[]},\"assistLine\":[],\"threshold\":{}}");
            chartViewWithBLOBs.setCustomStyle("{\"text\":{\"show\":false,\"fontSize\":\"18\",\"color\":\"#000000\",\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"remarkBackgroundColor\":\"#ffffffff\",\"fontFamily\":\"Microsoft YaHei\",\"letterSpace\":\"0\",\"fontShadow\":false,\"title\":\"分子总数\",\"modifyName\":\"show\",\"propertyName\":\"title-selector-ant-v\"},\"legend\":{\"show\":true,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"circle\",\"textStyle\":{\"color\":\"#000000\",\"fontSize\":\"12\"}},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxisExt\":{\"show\":true,\"position\":\"right\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"split\":{\"name\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#000000\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#000000\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true},\"axisValue\":{\"auto\":true,\"min\":10,\"max\":100}}}");
            chartViewWithBLOBs.setCustomAttr("{\"color\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\"},\"tableColor\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\"},\"size\":{\"barDefault\":true,\"barWidth\":40,\"barGap\":0.4,\"lineWidth\":2,\"lineType\":\"solid\",\"lineSymbol\":\"circle\",\"lineSymbolSize\":4,\"lineSmooth\":true,\"lineArea\":false,\"pieInnerRadius\":0,\"pieOuterRadius\":80,\"pieRoseType\":\"radius\",\"pieRoseRadius\":5,\"funnelWidth\":80,\"radarShape\":\"polygon\",\"radarSize\":80,\"tableTitleFontSize\":12,\"tableItemFontSize\":12,\"tableTitleHeight\":36,\"tableItemHeight\":36,\"tablePageSize\":\"20\",\"tableColumnMode\":\"custom\",\"tableColumnWidth\":100,\"tableHeaderAlign\":\"left\",\"tableItemAlign\":\"right\",\"tableAutoBreakLine\":false,\"tableRowTooltip\":{\"show\":false},\"tableColTooltip\":{\"show\":false},\"tableCellTooltip\":{\"show\":false},\"gaugeMinType\":\"fix\",\"gaugeMinField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMin\":0,\"gaugeMaxType\":\"fix\",\"gaugeMaxField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMax\":100,\"gaugeStartAngle\":225,\"gaugeEndAngle\":-45,\"gaugeAxisLine\":true,\"gaugeTickCount\":5,\"dimensionFontSize\":18,\"quotaFontSize\":\"60\",\"spaceSplit\":10,\"dimensionShow\":false,\"quotaShow\":true,\"quotaFontFamily\":\"Microsoft YaHei\",\"quotaFontIsBolder\":false,\"quotaFontIsItalic\":false,\"quotaLetterSpace\":\"0\",\"quotaFontShadow\":false,\"dimensionFontFamily\":\"Microsoft YaHei\",\"dimensionFontIsBolder\":false,\"dimensionFontIsItalic\":false,\"dimensionLetterSpace\":\"0\",\"dimensionFontShadow\":false,\"scatterSymbol\":\"circle\",\"scatterSymbolSize\":20,\"treemapWidth\":80,\"treemapHeight\":80,\"liquidMax\":100,\"liquidMaxType\":\"fix\",\"liquidMaxField\":{\"id\":\"\",\"summary\":\"\"},\"liquidSize\":80,\"liquidOutlineBorder\":4,\"liquidOutlineDistance\":8,\"liquidWaveLength\":128,\"liquidWaveCount\":3,\"liquidShape\":\"circle\",\"tablePageMode\":\"page\",\"symbolOpacity\":0.7,\"symbolStrokeWidth\":2,\"showIndex\":false,\"indexLabel\":\"序号\",\"hPosition\":\"center\",\"vPosition\":\"center\",\"mapPitch\":0,\"mapLineType\":\"arc\",\"mapLineWidth\":1,\"mapLineAnimate\":true,\"mapLineAnimateDuration\":3,\"mapLineAnimateInterval\":1,\"mapLineAnimateTrailLength\":1,\"wordSizeRange\":[8,32],\"wordSpacing\":6,\"showTableHeader\":true,\"quotaSuffix\":\"\",\"quotaSuffixFontSize\":12,\"quotaSuffixFontFamily\":\"Microsoft YaHei\",\"quotaSuffixFontIsItalic\":false,\"quotaSuffixFontIsBolder\":false,\"quotaSuffixLetterSpace\":\"0\",\"quotaSuffixFontShadow\":false,\"tableColumnFreezeHead\":0,\"tableColumnFreezeTail\":0,\"tableRowFreezeHead\":0,\"modifyName\":\"quotaFontSize\",\"propertyName\":\"size-selector-ant-v\"},\"label\":{\"show\":true,\"position\":\"middle\",\"color\":\"#000000\",\"fontSize\":\"10\",\"formatter\":\"{c}\",\"gaugeFormatter\":\"{value}\",\"labelLine\":{\"show\":true},\"gaugeLabelFormatter\":{\"type\":\"value\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"reserveDecimalCount\":2,\"labelContent\":[\"dimension\",\"proportion\"],\"modifyName\":\"gaugeLabelFormatter\",\"propertyName\":\"label-selector-ant-v\"},\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"confine\":true,\"textStyle\":{\"fontSize\":\"10\",\"color\":\"#000000\"},\"formatter\":\"\",\"backgroundColor\":\"#FFFFFF\"},\"totalCfg\":{\"row\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"},\"col\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"}}}");
        }
        if(chartView.getName().contains("分母总数")){
            Optional<DatasetTable> fzDatasetOpt = datasetTables.stream().filter(item -> item.getName().contains("分母")).findFirst();
            if(fzDatasetOpt.isPresent()){
                String tableId = fzDatasetOpt.get().getId();
                //设置分母总数
                chartViewWithBLOBs.setYAxis(buildCountAxis(tableId));
                chartViewWithBLOBs.setTableId(tableId);
            }
            chartViewWithBLOBs.setViewFields("[]");
            chartViewWithBLOBs.setXAxis("[]");
            chartViewWithBLOBs.setXAxisExt("[]");
            chartViewWithBLOBs.setYAxisExt("[]");
            chartViewWithBLOBs.setSenior("{\"functionCfg\":{\"sliderShow\":false,\"sliderRange\":[0,10],\"sliderBg\":\"#FFFFFF\",\"sliderFillBg\":\"#BCD6F1\",\"sliderTextClolor\":\"#999999\",\"emptyDataStrategy\":\"breakLine\",\"emptyDataFieldCtrl\":[]},\"assistLine\":[],\"threshold\":{}}");
            chartViewWithBLOBs.setCustomStyle("{\"text\":{\"show\":false,\"fontSize\":\"18\",\"color\":\"#000000\",\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"remarkBackgroundColor\":\"#ffffffff\",\"fontFamily\":\"Microsoft YaHei\",\"letterSpace\":\"0\",\"fontShadow\":false,\"title\":\"分母总数\",\"modifyName\":\"show\",\"propertyName\":\"title-selector-ant-v\"},\"legend\":{\"show\":true,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"circle\",\"textStyle\":{\"color\":\"#000000\",\"fontSize\":\"12\"}},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxisExt\":{\"show\":true,\"position\":\"right\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"split\":{\"name\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#000000\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#000000\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true},\"axisValue\":{\"auto\":true,\"min\":10,\"max\":100}}}");
            chartViewWithBLOBs.setCustomAttr("{ \"color\": { \"value\": \"default\", \"colors\": [\"#5470c6\", \"#91cc75\", \"#fac858\", \"#ee6666\", \"#73c0de\", \"#3ba272\", \"#fc8452\", \"#9a60b4\", \"#ea7ccc\"], \"alpha\": 100, \"tableHeaderBgColor\": \"#6D9A49\", \"tableItemBgColor\": \"#FFFFFF\", \"enableTableCrossBG\": false, \"tableItemSubBgColor\": \"#dedede\", \"tableHeaderFontColor\": \"#000000\", \"tableFontColor\": \"#000000\", \"tableStripe\": true, \"dimensionColor\": \"#000000\", \"quotaColor\": \"#FD9546\", \"tableBorderColor\": \"#E6E7E4\", \"seriesColors\": [], \"areaBorderColor\": \"#303133\", \"gradient\": false, \"areaBaseColor\": \"#FFFFFF\", \"tableScrollBarColor\": \"rgba(0, 0, 0, 0.15)\", \"tableScrollBarHoverColor\": \"rgba(0, 0, 0, 0.4)\", \"mapStyle\": \"normal\", \"mapLineGradient\": false, \"mapLineSourceColor\": \"#146C94\", \"mapLineTargetColor\": \"#576CBC\", \"quotaSuffixColor\": \"#5470c6\", \"modifyName\": \"colors\", \"propertyName\": \"color-selector\" }, \"tableColor\": { \"value\": \"default\", \"colors\": [\"#5470c6\", \"#91cc75\", \"#fac858\", \"#ee6666\", \"#73c0de\", \"#3ba272\", \"#fc8452\", \"#9a60b4\", \"#ea7ccc\"], \"alpha\": 100, \"tableHeaderBgColor\": \"#6D9A49\", \"tableItemBgColor\": \"#FFFFFF\", \"enableTableCrossBG\": false, \"tableItemSubBgColor\": \"#dedede\", \"tableHeaderFontColor\": \"#000000\", \"tableFontColor\": \"#000000\", \"tableStripe\": true, \"dimensionColor\": \"#000000\", \"quotaColor\": \"#5470c6\", \"tableBorderColor\": \"#E6E7E4\", \"seriesColors\": [], \"areaBorderColor\": \"#303133\", \"gradient\": false, \"areaBaseColor\": \"#FFFFFF\", \"tableScrollBarColor\": \"rgba(0, 0, 0, 0.15)\", \"tableScrollBarHoverColor\": \"rgba(0, 0, 0, 0.4)\", \"mapStyle\": \"normal\", \"mapLineGradient\": false, \"mapLineSourceColor\": \"#146C94\", \"mapLineTargetColor\": \"#576CBC\", \"quotaSuffixColor\": \"#5470c6\" }, \"size\": { \"barDefault\": true, \"barWidth\": 40, \"barGap\": 0.4, \"lineWidth\": 2, \"lineType\": \"solid\", \"lineSymbol\": \"circle\", \"lineSymbolSize\": 4, \"lineSmooth\": true, \"lineArea\": false, \"pieInnerRadius\": 0, \"pieOuterRadius\": 80, \"pieRoseType\": \"radius\", \"pieRoseRadius\": 5, \"funnelWidth\": 80, \"radarShape\": \"polygon\", \"radarSize\": 80, \"tableTitleFontSize\": 12, \"tableItemFontSize\": 12, \"tableTitleHeight\": 36, \"tableItemHeight\": 36, \"tablePageSize\": \"20\", \"tableColumnMode\": \"custom\", \"tableColumnWidth\": 100, \"tableHeaderAlign\": \"left\", \"tableItemAlign\": \"right\", \"tableAutoBreakLine\": false, \"tableRowTooltip\": { \"show\": false }, \"tableColTooltip\": { \"show\": false }, \"tableCellTooltip\": { \"show\": false }, \"gaugeMinType\": \"fix\", \"gaugeMinField\": { \"id\": \"\", \"summary\": \"\" }, \"gaugeMin\": 0, \"gaugeMaxType\": \"fix\", \"gaugeMaxField\": { \"id\": \"\", \"summary\": \"\" }, \"gaugeMax\": 100, \"gaugeStartAngle\": 225, \"gaugeEndAngle\": -45, \"gaugeAxisLine\": true, \"gaugeTickCount\": 5, \"dimensionFontSize\": 18, \"quotaFontSize\": \"60\", \"spaceSplit\": 10, \"dimensionShow\": false, \"quotaShow\": true, \"quotaFontFamily\": \"Microsoft YaHei\", \"quotaFontIsBolder\": false, \"quotaFontIsItalic\": false, \"quotaLetterSpace\": \"0\", \"quotaFontShadow\": false, \"dimensionFontFamily\": \"Microsoft YaHei\", \"dimensionFontIsBolder\": false, \"dimensionFontIsItalic\": false, \"dimensionLetterSpace\": \"0\", \"dimensionFontShadow\": false, \"scatterSymbol\": \"circle\", \"scatterSymbolSize\": 20, \"treemapWidth\": 80, \"treemapHeight\": 80, \"liquidMax\": 100, \"liquidMaxType\": \"fix\", \"liquidMaxField\": { \"id\": \"\", \"summary\": \"\" }, \"liquidSize\": 80, \"liquidOutlineBorder\": 4, \"liquidOutlineDistance\": 8, \"liquidWaveLength\": 128, \"liquidWaveCount\": 3, \"liquidShape\": \"circle\", \"tablePageMode\": \"page\", \"symbolOpacity\": 0.7, \"symbolStrokeWidth\": 2, \"showIndex\": false, \"indexLabel\": \"序号\", \"hPosition\": \"center\", \"vPosition\": \"center\", \"mapPitch\": 0, \"mapLineType\": \"arc\", \"mapLineWidth\": 1, \"mapLineAnimate\": true, \"mapLineAnimateDuration\": 3, \"mapLineAnimateInterval\": 1, \"mapLineAnimateTrailLength\": 1, \"wordSizeRange\": [8, 32], \"wordSpacing\": 6, \"showTableHeader\": true, \"quotaSuffix\": \"\", \"quotaSuffixFontSize\": 12, \"quotaSuffixFontFamily\": \"Microsoft YaHei\", \"quotaSuffixFontIsItalic\": false, \"quotaSuffixFontIsBolder\": false, \"quotaSuffixLetterSpace\": \"0\", \"quotaSuffixFontShadow\": false, \"tableColumnFreezeHead\": 0, \"tableColumnFreezeTail\": 0, \"tableRowFreezeHead\": 0, \"modifyName\": \"quotaFontSize\", \"propertyName\": \"size-selector-ant-v\" }, \"label\": { \"show\": true, \"position\": \"middle\", \"color\": \"#000000\", \"fontSize\": \"10\", \"formatter\": \"{c}\", \"gaugeFormatter\": \"{value}\", \"labelLine\": { \"show\": true }, \"gaugeLabelFormatter\": { \"type\": \"value\", \"unit\": 1, \"suffix\": \"\", \"decimalCount\": 2, \"thousandSeparator\": true }, \"reserveDecimalCount\": 2, \"labelContent\": [\"dimension\", \"proportion\"], \"modifyName\": \"gaugeLabelFormatter\", \"propertyName\": \"label-selector-ant-v\" }, \"tooltip\": { \"show\": true, \"trigger\": \"item\", \"confine\": true, \"textStyle\": { \"fontSize\": \"10\", \"color\": \"#000000\" }, \"formatter\": \"\", \"backgroundColor\": \"#FFFFFF\" }, \"totalCfg\": { \"row\": { \"showGrandTotals\": true, \"showSubTotals\": true, \"reverseLayout\": false, \"reverseSubLayout\": false, \"label\": \"总计\", \"subLabel\": \"小计\", \"subTotalsDimensions\": [], \"calcTotals\": { \"aggregation\": \"SUM\" }, \"calcSubTotals\": { \"aggregation\": \"SUM\" }, \"totalSort\": \"none\", \"totalSortField\": \"\" }, \"col\": { \"showGrandTotals\": true, \"showSubTotals\": true, \"reverseLayout\": false, \"reverseSubLayout\": false, \"label\": \"总计\", \"subLabel\": \"小计\", \"subTotalsDimensions\": [], \"calcTotals\": { \"aggregation\": \"SUM\" }, \"calcSubTotals\": { \"aggregation\": \"SUM\" }, \"totalSort\": \"none\", \"totalSortField\": \"\" } } }");
        }
        if(chartView.getName().contains("分子明细")){
            Optional<DatasetTable> fzDatasetOpt = datasetTables.stream().filter(item -> item.getName().contains("分子")).findFirst();
            if(fzDatasetOpt.isPresent()){
                String tableId = fzDatasetOpt.get().getId();
                //设置分子明细
                chartViewWithBLOBs.setXAxis(buildDetailsAxis(tableId));
                chartViewWithBLOBs.setTableId(tableId);
            }
            chartViewWithBLOBs.setSenior("{\"functionCfg\":{\"sliderShow\":false,\"sliderRange\":[0,10],\"sliderBg\":\"#FFFFFF\",\"sliderFillBg\":\"#BCD6F1\",\"sliderTextClolor\":\"#999999\",\"emptyDataStrategy\":\"breakLine\",\"emptyDataFieldCtrl\":[]},\"assistLine\":[],\"threshold\":{\"gaugeThreshold\":\"\",\"liquidThreshold\":\"\",\"labelThreshold\":[],\"tableThreshold\":[],\"textLabelThreshold\":[]}}");
            chartViewWithBLOBs.setCustomStyle("{\"text\":{\"show\":false,\"fontSize\":\"18\",\"color\":\"#000000\",\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"remarkBackgroundColor\":\"#ffffffff\",\"fontFamily\":\"Microsoft YaHei\",\"letterSpace\":\"0\",\"fontShadow\":false,\"title\":\"分子明细\",\"modifyName\":\"show\",\"propertyName\":\"title-selector-ant-v\"},\"legend\":{\"show\":true,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"circle\",\"textStyle\":{\"color\":\"#000000\",\"fontSize\":\"12\"}},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxisExt\":{\"show\":true,\"position\":\"right\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"split\":{\"name\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#000000\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#000000\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true},\"axisValue\":{\"auto\":true,\"min\":10,\"max\":100}}}");
            chartViewWithBLOBs.setCustomAttr("{\"color\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#EEF6FF\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\",\"modifyName\":\"colors\",\"propertyName\":\"color-selector\"},\"tableColor\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\"},\"size\":{\"barDefault\":true,\"barWidth\":40,\"barGap\":0.4,\"lineWidth\":2,\"lineType\":\"solid\",\"lineSymbol\":\"circle\",\"lineSymbolSize\":4,\"lineSmooth\":true,\"lineArea\":false,\"pieInnerRadius\":0,\"pieOuterRadius\":80,\"pieRoseType\":\"radius\",\"pieRoseRadius\":5,\"funnelWidth\":80,\"radarShape\":\"polygon\",\"radarSize\":80,\"tableTitleFontSize\":12,\"tableItemFontSize\":12,\"tableTitleHeight\":36,\"tableItemHeight\":36,\"tablePageSize\":\"10\",\"tableColumnMode\":\"adapt\",\"tableColumnWidth\":100,\"tableHeaderAlign\":\"center\",\"tableItemAlign\":\"center\",\"tableAutoBreakLine\":false,\"tableRowTooltip\":{\"show\":false},\"tableColTooltip\":{\"show\":false},\"tableCellTooltip\":{\"show\":false},\"gaugeMinType\":\"fix\",\"gaugeMinField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMin\":0,\"gaugeMaxType\":\"fix\",\"gaugeMaxField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMax\":100,\"gaugeStartAngle\":225,\"gaugeEndAngle\":-45,\"gaugeAxisLine\":true,\"gaugeTickCount\":5,\"dimensionFontSize\":18,\"quotaFontSize\":18,\"spaceSplit\":10,\"dimensionShow\":true,\"quotaShow\":true,\"quotaFontFamily\":\"Microsoft YaHei\",\"quotaFontIsBolder\":false,\"quotaFontIsItalic\":false,\"quotaLetterSpace\":\"0\",\"quotaFontShadow\":false,\"dimensionFontFamily\":\"Microsoft YaHei\",\"dimensionFontIsBolder\":false,\"dimensionFontIsItalic\":false,\"dimensionLetterSpace\":\"0\",\"dimensionFontShadow\":false,\"scatterSymbol\":\"circle\",\"scatterSymbolSize\":20,\"treemapWidth\":80,\"treemapHeight\":80,\"liquidMax\":100,\"liquidMaxType\":\"fix\",\"liquidMaxField\":{\"id\":\"\",\"summary\":\"\"},\"liquidSize\":80,\"liquidOutlineBorder\":4,\"liquidOutlineDistance\":8,\"liquidWaveLength\":128,\"liquidWaveCount\":3,\"liquidShape\":\"circle\",\"tablePageMode\":\"page\",\"symbolOpacity\":0.7,\"symbolStrokeWidth\":2,\"showIndex\":true,\"indexLabel\":\"序号\",\"hPosition\":\"center\",\"vPosition\":\"center\",\"mapPitch\":0,\"mapLineType\":\"arc\",\"mapLineWidth\":1,\"mapLineAnimate\":true,\"mapLineAnimateDuration\":3,\"mapLineAnimateInterval\":1,\"mapLineAnimateTrailLength\":1,\"wordSizeRange\":[8,32],\"wordSpacing\":6,\"showTableHeader\":true,\"quotaSuffix\":\"\",\"quotaSuffixFontSize\":12,\"quotaSuffixFontFamily\":\"Microsoft YaHei\",\"quotaSuffixFontIsItalic\":false,\"quotaSuffixFontIsBolder\":false,\"quotaSuffixLetterSpace\":\"0\",\"quotaSuffixFontShadow\":false,\"tableColumnFreezeHead\":0,\"tableColumnFreezeTail\":0,\"tableRowFreezeHead\":0,\"modifyName\":\"tableColumnMode\",\"propertyName\":\"size-selector-ant-v\"},\"label\":{\"show\":false,\"position\":\"middle\",\"color\":\"#000000\",\"fontSize\":\"10\",\"formatter\":\"{c}\",\"gaugeFormatter\":\"{value}\",\"labelLine\":{\"show\":true},\"gaugeLabelFormatter\":{\"type\":\"value\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"reserveDecimalCount\":2,\"labelContent\":[\"dimension\",\"proportion\"]},\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"confine\":true,\"textStyle\":{\"fontSize\":\"10\",\"color\":\"#000000\"},\"formatter\":\"\",\"backgroundColor\":\"#FFFFFF\"},\"totalCfg\":{\"row\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"},\"col\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"}}}");
        }
        if(chartView.getName().contains("分母明细")){
            Optional<DatasetTable> fzDatasetOpt = datasetTables.stream().filter(item -> item.getName().contains("分母")).findFirst();
            if(fzDatasetOpt.isPresent()){
                String tableId = fzDatasetOpt.get().getId();
                //设置分母明细
                chartViewWithBLOBs.setXAxis(buildDetailsAxis(tableId));
                chartViewWithBLOBs.setTableId(tableId);
            }

            chartViewWithBLOBs.setSenior("{\"functionCfg\":{\"sliderShow\":false,\"sliderRange\":[0,10],\"sliderBg\":\"#FFFFFF\",\"sliderFillBg\":\"#BCD6F1\",\"sliderTextClolor\":\"#999999\",\"emptyDataStrategy\":\"breakLine\",\"emptyDataFieldCtrl\":[]},\"assistLine\":[],\"threshold\":{\"gaugeThreshold\":\"\",\"liquidThreshold\":\"\",\"labelThreshold\":[],\"tableThreshold\":[],\"textLabelThreshold\":[]}}");
            chartViewWithBLOBs.setCustomStyle("{\"text\":{\"show\":false,\"fontSize\":\"18\",\"color\":\"#000000\",\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"remarkBackgroundColor\":\"#ffffffff\",\"fontFamily\":\"Microsoft YaHei\",\"letterSpace\":\"0\",\"fontShadow\":false,\"title\":\"分母明细\",\"modifyName\":\"show\",\"propertyName\":\"title-selector-ant-v\"},\"legend\":{\"show\":true,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"circle\",\"textStyle\":{\"color\":\"#000000\",\"fontSize\":\"12\"}},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxisExt\":{\"show\":true,\"position\":\"right\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"split\":{\"name\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#000000\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#000000\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true},\"axisValue\":{\"auto\":true,\"min\":10,\"max\":100}}}");
            chartViewWithBLOBs.setCustomAttr("{\"color\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#EEF6FF\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\",\"modifyName\":\"colors\",\"propertyName\":\"color-selector\"},\"tableColor\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\"},\"size\":{\"barDefault\":true,\"barWidth\":40,\"barGap\":0.4,\"lineWidth\":2,\"lineType\":\"solid\",\"lineSymbol\":\"circle\",\"lineSymbolSize\":4,\"lineSmooth\":true,\"lineArea\":false,\"pieInnerRadius\":0,\"pieOuterRadius\":80,\"pieRoseType\":\"radius\",\"pieRoseRadius\":5,\"funnelWidth\":80,\"radarShape\":\"polygon\",\"radarSize\":80,\"tableTitleFontSize\":12,\"tableItemFontSize\":12,\"tableTitleHeight\":36,\"tableItemHeight\":36,\"tablePageSize\":\"10\",\"tableColumnMode\":\"adapt\",\"tableColumnWidth\":100,\"tableHeaderAlign\":\"center\",\"tableItemAlign\":\"center\",\"tableAutoBreakLine\":false,\"tableRowTooltip\":{\"show\":false},\"tableColTooltip\":{\"show\":false},\"tableCellTooltip\":{\"show\":false},\"gaugeMinType\":\"fix\",\"gaugeMinField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMin\":0,\"gaugeMaxType\":\"fix\",\"gaugeMaxField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMax\":100,\"gaugeStartAngle\":225,\"gaugeEndAngle\":-45,\"gaugeAxisLine\":true,\"gaugeTickCount\":5,\"dimensionFontSize\":18,\"quotaFontSize\":18,\"spaceSplit\":10,\"dimensionShow\":true,\"quotaShow\":true,\"quotaFontFamily\":\"Microsoft YaHei\",\"quotaFontIsBolder\":false,\"quotaFontIsItalic\":false,\"quotaLetterSpace\":\"0\",\"quotaFontShadow\":false,\"dimensionFontFamily\":\"Microsoft YaHei\",\"dimensionFontIsBolder\":false,\"dimensionFontIsItalic\":false,\"dimensionLetterSpace\":\"0\",\"dimensionFontShadow\":false,\"scatterSymbol\":\"circle\",\"scatterSymbolSize\":20,\"treemapWidth\":80,\"treemapHeight\":80,\"liquidMax\":100,\"liquidMaxType\":\"fix\",\"liquidMaxField\":{\"id\":\"\",\"summary\":\"\"},\"liquidSize\":80,\"liquidOutlineBorder\":4,\"liquidOutlineDistance\":8,\"liquidWaveLength\":128,\"liquidWaveCount\":3,\"liquidShape\":\"circle\",\"tablePageMode\":\"page\",\"symbolOpacity\":0.7,\"symbolStrokeWidth\":2,\"showIndex\":true,\"indexLabel\":\"序号\",\"hPosition\":\"center\",\"vPosition\":\"center\",\"mapPitch\":0,\"mapLineType\":\"arc\",\"mapLineWidth\":1,\"mapLineAnimate\":true,\"mapLineAnimateDuration\":3,\"mapLineAnimateInterval\":1,\"mapLineAnimateTrailLength\":1,\"wordSizeRange\":[8,32],\"wordSpacing\":6,\"showTableHeader\":true,\"quotaSuffix\":\"\",\"quotaSuffixFontSize\":12,\"quotaSuffixFontFamily\":\"Microsoft YaHei\",\"quotaSuffixFontIsItalic\":false,\"quotaSuffixFontIsBolder\":false,\"quotaSuffixLetterSpace\":\"0\",\"quotaSuffixFontShadow\":false,\"tableColumnFreezeHead\":0,\"tableColumnFreezeTail\":0,\"tableRowFreezeHead\":0,\"modifyName\":\"tableColumnMode\",\"propertyName\":\"size-selector-ant-v\"},\"label\":{\"show\":false,\"position\":\"middle\",\"color\":\"#000000\",\"fontSize\":\"10\",\"formatter\":\"{c}\",\"gaugeFormatter\":\"{value}\",\"labelLine\":{\"show\":true},\"gaugeLabelFormatter\":{\"type\":\"value\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"reserveDecimalCount\":2,\"labelContent\":[\"dimension\",\"proportion\"]},\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"confine\":true,\"textStyle\":{\"fontSize\":\"10\",\"color\":\"#000000\"},\"formatter\":\"\",\"backgroundColor\":\"#FFFFFF\"},\"totalCfg\":{\"row\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"},\"col\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"}}}");
        }
        if(chartView.getName().contains("比率")){
            Optional<DatasetTable> fzDatasetOpt = datasetTables.stream().filter(item -> item.getName().contains("比率")).findFirst();
            if(fzDatasetOpt.isPresent()){
                String tableId = fzDatasetOpt.get().getId();
                //设置比率-维度
                chartViewWithBLOBs.setYAxis(buildRateYAxis(tableId));
                //设置比率-指标
                chartViewWithBLOBs.setXAxis(buildRateXAxis(tableId));
                chartViewWithBLOBs.setTableId(tableId);
            }
            chartViewWithBLOBs.setSenior("{\"functionCfg\":{\"sliderShow\":false,\"sliderRange\":[0,10],\"sliderBg\":\"#FFFFFF\",\"sliderFillBg\":\"#BCD6F1\",\"sliderTextClolor\":\"#999999\",\"emptyDataStrategy\":\"setZero\",\"emptyDataFieldCtrl\":[]},\"assistLine\":[],\"threshold\":{\"gaugeThreshold\":\"\",\"liquidThreshold\":\"\",\"labelThreshold\":[],\"tableThreshold\":[],\"textLabelThreshold\":[]}}");
            chartViewWithBLOBs.setCustomStyle("{\"text\":{\"show\":false,\"fontSize\":\"18\",\"color\":\"#000000\",\"hPosition\":\"left\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":true,\"remarkShow\":false,\"remark\":\"\",\"remarkBackgroundColor\":\"#ffffffff\",\"fontFamily\":\"Microsoft YaHei\",\"letterSpace\":\"0\",\"fontShadow\":false,\"title\":\"比率\",\"modifyName\":\"show\",\"propertyName\":\"title-selector-ant-v\"},\"legend\":{\"show\":false,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"circle\",\"textStyle\":{\"color\":\"#000000\",\"fontSize\":\"12\"},\"modifyName\":\"show\",\"propertyName\":\"legend-selector-ant-v\"},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\",\"lengthLimit\":20},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":true,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"percent\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":0,\"thousandSeparator\":true},\"modifyName\":\"axisLabelFormatter\",\"propertyName\":\"y-axis-selector-ant-v\"},\"yAxisExt\":{\"show\":true,\"position\":\"right\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#000000\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"axisLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"style\":\"solid\"},\"enableDash\":false,\"dashStyle\":{\"width\":4,\"offset\":5}},\"axisValue\":{\"auto\":true,\"min\":null,\"max\":null,\"split\":null,\"splitCount\":null},\"axisLabelFormatter\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true}},\"split\":{\"name\":{\"show\":true,\"color\":\"#000000\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#000000\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#000000\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#CCCCCC\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true},\"axisValue\":{\"auto\":true,\"min\":10,\"max\":100}}}");
            chartViewWithBLOBs.setCustomAttr("{\"color\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[{\"name\":\"比率\",\"color\":\"#FD9546\",\"isCustom\":true}],\"areaBorderColor\":\"#303133\",\"gradient\":true,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\",\"modifyName\":\"seriesColors\",\"propertyName\":\"color-selector\"},\"tableColor\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#6D9A49\",\"tableItemBgColor\":\"#FFFFFF\",\"enableTableCrossBG\":false,\"tableItemSubBgColor\":\"#dedede\",\"tableHeaderFontColor\":\"#000000\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#5470c6\",\"tableBorderColor\":\"#E6E7E4\",\"seriesColors\":[],\"areaBorderColor\":\"#303133\",\"gradient\":false,\"areaBaseColor\":\"#FFFFFF\",\"tableScrollBarColor\":\"rgba(0, 0, 0, 0.15)\",\"tableScrollBarHoverColor\":\"rgba(0, 0, 0, 0.4)\",\"mapStyle\":\"normal\",\"mapLineGradient\":false,\"mapLineSourceColor\":\"#146C94\",\"mapLineTargetColor\":\"#576CBC\",\"quotaSuffixColor\":\"#5470c6\"},\"size\":{\"barDefault\":true,\"barWidth\":40,\"barGap\":0.4,\"lineWidth\":2,\"lineType\":\"solid\",\"lineSymbol\":\"circle\",\"lineSymbolSize\":6,\"lineSmooth\":true,\"lineArea\":true,\"pieInnerRadius\":0,\"pieOuterRadius\":80,\"pieRoseType\":\"radius\",\"pieRoseRadius\":5,\"funnelWidth\":80,\"radarShape\":\"polygon\",\"radarSize\":80,\"tableTitleFontSize\":12,\"tableItemFontSize\":12,\"tableTitleHeight\":36,\"tableItemHeight\":36,\"tablePageSize\":\"20\",\"tableColumnMode\":\"custom\",\"tableColumnWidth\":100,\"tableHeaderAlign\":\"left\",\"tableItemAlign\":\"right\",\"tableAutoBreakLine\":false,\"tableRowTooltip\":{\"show\":false},\"tableColTooltip\":{\"show\":false},\"tableCellTooltip\":{\"show\":false},\"gaugeMinType\":\"fix\",\"gaugeMinField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMin\":0,\"gaugeMaxType\":\"fix\",\"gaugeMaxField\":{\"id\":\"\",\"summary\":\"\"},\"gaugeMax\":100,\"gaugeStartAngle\":225,\"gaugeEndAngle\":-45,\"gaugeAxisLine\":true,\"gaugeTickCount\":5,\"dimensionFontSize\":18,\"quotaFontSize\":18,\"spaceSplit\":10,\"dimensionShow\":true,\"quotaShow\":true,\"quotaFontFamily\":\"Microsoft YaHei\",\"quotaFontIsBolder\":false,\"quotaFontIsItalic\":false,\"quotaLetterSpace\":\"0\",\"quotaFontShadow\":false,\"dimensionFontFamily\":\"Microsoft YaHei\",\"dimensionFontIsBolder\":false,\"dimensionFontIsItalic\":false,\"dimensionLetterSpace\":\"0\",\"dimensionFontShadow\":false,\"scatterSymbol\":\"circle\",\"scatterSymbolSize\":20,\"treemapWidth\":80,\"treemapHeight\":80,\"liquidMax\":100,\"liquidMaxType\":\"fix\",\"liquidMaxField\":{\"id\":\"\",\"summary\":\"\"},\"liquidSize\":80,\"liquidOutlineBorder\":4,\"liquidOutlineDistance\":8,\"liquidWaveLength\":128,\"liquidWaveCount\":3,\"liquidShape\":\"circle\",\"tablePageMode\":\"page\",\"symbolOpacity\":0.7,\"symbolStrokeWidth\":2,\"showIndex\":false,\"indexLabel\":\"序号\",\"hPosition\":\"center\",\"vPosition\":\"center\",\"mapPitch\":0,\"mapLineType\":\"arc\",\"mapLineWidth\":1,\"mapLineAnimate\":true,\"mapLineAnimateDuration\":3,\"mapLineAnimateInterval\":1,\"mapLineAnimateTrailLength\":1,\"wordSizeRange\":[8,32],\"wordSpacing\":6,\"showTableHeader\":true,\"quotaSuffix\":\"\",\"quotaSuffixFontSize\":12,\"quotaSuffixFontFamily\":\"Microsoft YaHei\",\"quotaSuffixFontIsItalic\":false,\"quotaSuffixFontIsBolder\":false,\"quotaSuffixLetterSpace\":\"0\",\"quotaSuffixFontShadow\":false,\"tableColumnFreezeHead\":0,\"tableColumnFreezeTail\":0,\"tableRowFreezeHead\":0,\"modifyName\":\"lineSymbolSize\",\"propertyName\":\"size-selector-ant-v\"},\"label\":{\"show\":false,\"position\":\"top\",\"color\":\"#000000\",\"fontSize\":\"10\",\"formatter\":\"{c}\",\"gaugeFormatter\":\"{value}\",\"labelLine\":{\"show\":true},\"gaugeLabelFormatter\":{\"type\":\"value\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"reserveDecimalCount\":2,\"labelContent\":[\"dimension\",\"proportion\"]},\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"confine\":true,\"textStyle\":{\"fontSize\":\"12\",\"color\":\"#000000\"},\"formatter\":\"\",\"backgroundColor\":\"#FFFFFF\",\"modifyName\":\"textStyle\",\"propertyName\":\"tooltip-selector-ant-v\"},\"totalCfg\":{\"row\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"},\"col\":{\"showGrandTotals\":true,\"showSubTotals\":true,\"reverseLayout\":false,\"reverseSubLayout\":false,\"label\":\"总计\",\"subLabel\":\"小计\",\"subTotalsDimensions\":[],\"calcTotals\":{\"aggregation\":\"SUM\"},\"calcSubTotals\":{\"aggregation\":\"SUM\"},\"totalSort\":\"none\",\"totalSortField\":\"\"}}}");
        }

        return chartViewWithBLOBs;
    }

    //构建编辑视图信息入参 - 单病种模板
    private ChartViewWithBLOBs buildSingleDiseaseChartViewEditParam(ChartViewWithBLOBs chartView, List<DatasetTable> datasetTables) {
        ChartViewWithBLOBs chartViewNew = new ChartViewWithBLOBs();
        BeanUtils.copyBean(chartViewNew,chartView);
        //设置数据来源为数据集
        chartViewNew.setDataFrom(CommonConstants.VIEW_DATA_FROM.CHART);
        if(chartView.getName().equals("出院人数")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "出院人数".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置出院人数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"NUM"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("总住院费")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "住院费".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置出院人数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"TOTAL_COST"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("平均住院费")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "住院费".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置出院人数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"TOTAL_COST"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("总床日数")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "住院日".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //总床日数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"IN_DAYS"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("床位数")){
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "住院日".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置患者数
                chartViewNew.setYAxis(buildCountAxis(datasetTable.getId()));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("平均住院日")){
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "住院日".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置患者数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"IN_DAYS"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }

        if(chartView.getName().equals("死亡人数")){
            //根据不同的视图查询数据集
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "死亡".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //总床日数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"DIE_NUM"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("总人数")){
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "死亡".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置患者数
                chartViewNew.setYAxis(buildCountAxis(datasetTable.getId()));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        if(chartView.getName().equals("平均死亡率")){
            Optional<DatasetTable> datasetOpt = datasetTables
                    .stream()
                    .filter(item -> "死亡".equals(item.getName())).findFirst();
            if(datasetOpt.isPresent()){
                DatasetTable datasetTable = datasetOpt.get();
                //设置患者数
                chartViewNew.setYAxis(buildGeneralNumCountAxis(datasetTable,chartView,"DIE_NUM"));
                chartViewNew.setTableId(datasetTable.getId());
            }
        }
        return chartViewNew;
    }

    /**
     * 构建分子明细
     * @param tableId
     * @return
     */
    private String buildDetailsAxis(String tableId) {
        //数据集字段信息
        ArrayList<GeneralAxisDataDTO> detailsAxisFields = new ArrayList<>();

        DatasetTableFieldExample datasetTableFieldExample = new DatasetTableFieldExample();
        datasetTableFieldExample.createCriteria().andTableIdEqualTo(tableId);
        List<DatasetTableField> detailsFieldList = datasetTableFieldMapper.selectByExample(datasetTableFieldExample);
        detailsFieldList = detailsFieldList.stream()
                .filter(item -> !List.of("X_VALUE","Y_VALUE").contains(item.getName()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(detailsFieldList)){
            detailsFieldList.forEach(item -> {
                GeneralAxisDataDTO axisDataDTO = new GeneralAxisDataDTO();
                BeanUtils.copyBean(axisDataDTO,item);
                detailsAxisFields.add(axisDataDTO);
            });
        }
        return JSON.toJSONString(detailsAxisFields);
    }

    /**
     * 通用_数据集记录总数Axis
     * @param tableId TableId
     * @return
     */
    private String buildCountAxis(String tableId) {
        String template = "[{\"id\":\"count\",\"tableId\":\"{tableId}\",\"originName\":\"*\",\"name\":\"记录数*\",\"dataeaseName\":\"*\",\"groupType\":\"q\",\"type\":\"INT\",\"size\":null,\"deType\":2,\"deTypeFormat\":null,\"deExtractType\":null,\"extField\":1,\"checked\":true,\"columnIndex\":999,\"lastSyncTime\":null,\"accuracy\":null,\"dateFormat\":null,\"dateFormatType\":null,\"sort\":\"none\",\"chartType\":\"bar\",\"summary\":\"count\",\"filter\":[],\"compareCalc\":{\"type\":\"none\",\"resultData\":\"percent\",\"field\":\"\",\"custom\":{\"field\":\"\",\"calcType\":\"0\",\"timeType\":\"0\",\"currentTime\":\"\",\"compareTime\":\"\",\"currentTimeRange\":[],\"compareTimeRange\":[]}}}]";
        return template.replace("{tableId}", tableId);
    }

    /**
     * 通用_数据集数值计算 平均,求和 Axis
     * @param datasetTable 数据集信息
     * @param chartView 视图信息
     * @return
     */
    private String buildGeneralNumCountAxis(DatasetTable datasetTable,ChartViewWithBLOBs chartView,String filterFieldName) {
        try {
            //数据集字段信息
            DatasetTableFieldExample datasetTableFieldExample = new DatasetTableFieldExample();
            datasetTableFieldExample.createCriteria().andTableIdEqualTo(datasetTable.getId());
            List<DatasetTableField> detailsFieldList = datasetTableFieldMapper.selectByExample(datasetTableFieldExample);
            Optional<DatasetTableField> opt = detailsFieldList.stream()
                    .filter(item -> filterFieldName.equals(item.getOriginName()))
                    .findFirst();
            DatasetTableField datasetTableField = null;
            if(opt.isPresent()){
                datasetTableField = opt.get();
            }
            if(Objects.nonNull(datasetTableField)){
                List<GeneralAxisDataDTO> yAxisList = gson.fromJson(chartView.getYAxis(),
                        new TypeToken<List<GeneralAxisDataDTO>>() {}.getType());
                if (!yAxisList.isEmpty()) {
                    GeneralAxisDataDTO originYAxis = yAxisList.get(0);
                    GeneralAxisDataDTO newYAxis = BeanUtils.copyBean(originYAxis,datasetTableField);
                    return JSON.toJSONString(Lists.newArrayList(newYAxis));
                }
            }
        } catch (Exception e) {
            log.info("buildSumCountAxis exception,info:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 比率_创建X Axis
     * @param tableId TableId
     * @return
     */
    private String buildRateXAxis(String tableId) {
        //数据集字段信息
        DatasetTableFieldExample datasetTableFieldExample = new DatasetTableFieldExample();
        datasetTableFieldExample.createCriteria().andTableIdEqualTo(tableId);
        List<DatasetTableField> rateFieldList = datasetTableFieldMapper.selectByExample(datasetTableFieldExample);
        Optional<DatasetTableField> xValueOpt = rateFieldList.stream().filter(item -> item.getName().equalsIgnoreCase("X_VALUE")).findFirst();
        String template = "[{\"id\":\"{id}\",\"tableId\":\"{tableId}\",\"originName\":\"{originName}\",\"name\":\"{name}\",\"dataeaseName\":\"{dataeaseName}\",\"groupType\":\"{groupType}\",\"type\":\"{type}\",\"size\":{size},\"deType\":{deType},\"deTypeFormat\":{deTypeFormat},\"deExtractType\":{deExtractType},\"extField\":{extField},\"checked\":true,\"columnIndex\":1,\"lastSyncTime\":1749453189796,\"accuracy\":0,\"dateFormat\":{dateFormat},\"dateFormatType\":{dateFormatType},\"disabled\":true,\"formatterCfg\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"dateStyle\":\"y_M_d\",\"datePattern\":\"date_sub\",\"sort\":\"none\",\"filter\":[]}]";
        if(xValueOpt.isPresent()){
            DatasetTableField datasetTableField = xValueOpt.get();
            template = template.replace("{id}", datasetTableField.getId())
                    .replace("{tableId}", datasetTableField.getTableId())
                    .replace("{originName}", datasetTableField.getOriginName())
                    .replace("{name}", datasetTableField.getName())
                    .replace("{dataeaseName}", datasetTableField.getDataeaseName())
                    .replace("{groupType}", datasetTableField.getGroupType())
                    .replace("{type}", datasetTableField.getType())
                    .replace("{size}", nonNull(datasetTableField.getSize()) ? String.valueOf(datasetTableField.getSize().intValue()) : "0")
                    .replace("{deType}",nonNull(datasetTableField.getDeType()) ? String.valueOf(datasetTableField.getDeType().intValue()) : "0")
                    .replace("{deTypeFormat}",nonNull(datasetTableField.getDeTypeFormat()) ? String.valueOf(datasetTableField.getDeTypeFormat().intValue()) : "null")
                    .replace("{deExtractType}", nonNull(datasetTableField.getDeExtractType()) ? String.valueOf(datasetTableField.getDeExtractType().intValue()) : "0")
                    .replace("{extField}", nonNull(datasetTableField.getExtField()) ? String.valueOf(datasetTableField.getExtField().intValue()) : "0")
                    .replace("{dateFormat}",nonNull(datasetTableField.getDateFormat()) ? datasetTableField.getDateFormat() : "null" )
                    .replace("{dateFormatType}", nonNull(datasetTableField.getDateFormatType()) ? datasetTableField.getDateFormatType() : "null" );
        }
        return template;
    }

    /**
     * 比率_创建Y Axis
     * @param tableId TableId
     * @return
     */
    private String buildRateYAxis(String tableId) {
        //数据集字段信息
        DatasetTableFieldExample datasetTableFieldExample = new DatasetTableFieldExample();
        datasetTableFieldExample.createCriteria().andTableIdEqualTo(tableId);
        List<DatasetTableField> rateFieldList = datasetTableFieldMapper.selectByExample(datasetTableFieldExample);
        Optional<DatasetTableField> yValueOpt = rateFieldList.stream().filter(item -> item.getName().equalsIgnoreCase("Y_VALUE")).findFirst();
        String template = "[{\"id\":\"{id}\",\"tableId\":\"{tableId}\",\"originName\":\"{originName}\",\"name\":\"{name}\",\"dataeaseName\":\"{dataeaseName}\",\"groupType\":\"q\",\"type\":\"NUMBER\",\"size\":39,\"deType\":3,\"deTypeFormat\":null,\"deExtractType\":3,\"extField\":0,\"checked\":true,\"columnIndex\":3,\"lastSyncTime\":1749437975555,\"accuracy\":0,\"dateFormat\":null,\"dateFormatType\":null,\"sort\":\"none\",\"compareCalc\":{\"type\":\"none\",\"resultData\":\"percent\",\"field\":\"\",\"custom\":{\"field\":\"\",\"calcType\":\"0\",\"timeType\":\"0\",\"currentTime\":\"\",\"compareTime\":\"\",\"currentTimeRange\":[],\"compareTimeRange\":[]}},\"formatterCfg\":{\"type\":\"auto\",\"unit\":1,\"suffix\":\"\",\"decimalCount\":2,\"thousandSeparator\":true},\"chartType\":\"bar\",\"summary\":\"sum\",\"filter\":[]}]";
        if(yValueOpt.isPresent()){
            DatasetTableField datasetTableField = yValueOpt.get();
            template = template.replace("{id}", datasetTableField.getId())
                               .replace("{tableId}", datasetTableField.getTableId())
                               .replace("{originName}", datasetTableField.getOriginName())
                               .replace("{name}", datasetTableField.getName())
                               .replace("{dataeaseName}", datasetTableField.getDataeaseName());
        }
        return template;
    }

    /**
     * 基于模板复制仪表板
     * @param top3IndexInfo 三甲指标任务信息
     * @param panelGroupInfo 仪表板模板信息
     * @return
     */
    private String copyTemplatePanel(Top3IndexInfoDTO top3IndexInfo, PanelGroupWithBLOBs panelGroupInfo) {
        //该指标在仪表板已存在则删除
        PanelGroupExample panelGroupExample = new PanelGroupExample();
        panelGroupExample.createCriteria().andPidEqualTo(panelGroupInfo.getId());
        panelGroupMapper.deleteByExample(panelGroupExample);
        PanelGroupRequest groupRequest = new PanelGroupRequest();
        // 根据不同的类型判断使用不同的模板类型
        groupRequest.setTemplateId(Top3IndexTemplateEnum.RATE.getCode().equals(top3IndexInfo.getIndexType()) ? rateTemplateId : singleDiseaseTemplateId);
        groupRequest.setLevel(1);
        groupRequest.setName(top3IndexInfo.getIndexName());
        groupRequest.setNewFrom("new_inner_template");
        groupRequest.setNodeType("panel");
        groupRequest.setPanelType("self");
        groupRequest.setPid(panelGroupInfo.getId());
        return panelGroupService.save(groupRequest);
    }

    /**
     * 构建指标仪表板分组信息
     * @param top3IndexInfo 三甲指标信息
     * @return
     */
    private PanelGroupWithBLOBs buildPanelGroupInfo(Top3IndexInfoDTO top3IndexInfo) {
        //1.top group
        PanelGroupExample panelGroupExample = new PanelGroupExample();
        panelGroupExample.createCriteria()
                        .andPidEqualTo("panel_list")
                        .andNodeTypeEqualTo("folder")
                        .andNameEqualTo("医院等级评审系统_自动生成");
        List<PanelGroupWithBLOBs> topPanelGroupList = panelGroupMapper.selectByExampleWithBLOBs(panelGroupExample);
        PanelGroupWithBLOBs topPanelGroup = new PanelGroupWithBLOBs();
        if(CollectionUtils.isEmpty(topPanelGroupList)){
            //没有则创建
            topPanelGroup.setId(UUID.randomUUID().toString());
            topPanelGroup.setName("医院等级评审系统_自动生成");
            topPanelGroup.setPid("panel_list");
            topPanelGroup.setLevel(0);
            topPanelGroup.setNodeType("folder");
            topPanelGroup.setCreateBy("admin");
            topPanelGroup.setCreateTime(System.currentTimeMillis());
            topPanelGroup.setPanelType("self");
            topPanelGroup.setPanelStyle("{}");
            topPanelGroup.setPanelData("[]");
            sysAuthService.copyAuth(topPanelGroup.getId(), SysAuthConstants.AUTH_SOURCE_TYPE_PANEL);
            panelGroupMapper.insert(topPanelGroup);
        }else{
            topPanelGroup = topPanelGroupList.get(0);
        }
        //2.second group
        PanelGroupExample secondPanelGroupExample = new PanelGroupExample();
        secondPanelGroupExample.createCriteria()
                .andPidEqualTo(topPanelGroup.getId())
                .andLevelEqualTo(1)
                .andNameEqualTo(top3IndexInfo.getChapterName());
        List<PanelGroupWithBLOBs> secondPanelGroupList = panelGroupMapper.selectByExampleWithBLOBs(secondPanelGroupExample);
        PanelGroupWithBLOBs secondPanelGroup = new PanelGroupWithBLOBs();
        if(CollectionUtils.isEmpty(secondPanelGroupList)){
            //没有则创建
            secondPanelGroup.setId(UUID.randomUUID().toString());
            secondPanelGroup.setName(top3IndexInfo.getChapterName());
            secondPanelGroup.setPid(topPanelGroup.getId());
            secondPanelGroup.setLevel(1);
            secondPanelGroup.setNodeType("folder");
            secondPanelGroup.setCreateBy("admin");
            secondPanelGroup.setCreateTime(System.currentTimeMillis());
            secondPanelGroup.setPanelType("self");
            secondPanelGroup.setPanelStyle("{}");
            secondPanelGroup.setPanelData("[]");
            sysAuthService.copyAuth(secondPanelGroup.getId(), SysAuthConstants.AUTH_SOURCE_TYPE_PANEL);
            panelGroupMapper.insert(secondPanelGroup);
        }else{
            secondPanelGroup = secondPanelGroupList.get(0);
        }
        return secondPanelGroup;
    }

    //保存指标数据集
    private List<DatasetTable> saveIndexDataset(String datasourceId, DatasetGroup datasetGroupIndexInfo, Top3IndexInfoDTO taskInfo) {
        List<DatasetTable> datasetTableList = new ArrayList<>();
        if(CollectionUtils.isEmpty(taskInfo.getDataSetList())){
            throw new IllegalArgumentException("三甲评审指标生成失败_当前指标数据集为空!");
        }
        //该指标下数据集已存在则先删除
        DatasetTableExample tableExample = new DatasetTableExample();
        tableExample.createCriteria().andSceneIdEqualTo(datasetGroupIndexInfo.getId());
        datasetTableMapper.deleteByExample(tableExample);
        for (Top3IndexDataSetInfoDTO dataSetInfo : taskInfo.getDataSetList()) {
            try {
                DataSetTableRequest dataSetTableRequest = buildDatasetSaveParam(datasourceId, datasetGroupIndexInfo, dataSetInfo);
                DatasetTable datasetTable = dataSetTableService.save(dataSetTableRequest);
                datasetTableList.add(datasetTable);
            }catch (Exception e){
                log.error("三甲评审指标生成失败_指标名称:{}",taskInfo.getIndexName());
                throw new IllegalArgumentException("三甲评审指标生成失败_指标数据集生成异常:" + e.getMessage(),e.getCause());
            }
        }
        return datasetTableList;
    }

    /**
     * 构建数据集保存入参
     * @param datasourceId 数据源ID
     * @param datasetGroupIndexInfo 指标数据集分组id
     * @param datasetInfo 指标数据集信息
     * @return
     */
    private DataSetTableRequest buildDatasetSaveParam(String datasourceId, DatasetGroup datasetGroupIndexInfo,Top3IndexDataSetInfoDTO datasetInfo) {
        DataSetTableRequest dataSetTableRequest = new DataSetTableRequest();
        dataSetTableRequest.setDataSourceId(datasourceId);
        dataSetTableRequest.setMode(0);
        dataSetTableRequest.setName(datasetInfo.getDatasetName());
        dataSetTableRequest.setSceneId(datasetGroupIndexInfo.getId());
        dataSetTableRequest.setSqlVariableDetails("[]");
        dataSetTableRequest.setSyncType("sync_now");
        dataSetTableRequest.setType("sql");
        dataSetTableRequest.setInfo(buildSqlInfo(datasetInfo.getDatasetSql()));
        return dataSetTableRequest;
    }

    /**
     * 保存指标数据集_构建sql入参
     * @param datasetSql
     * @return
     */
    private String buildSqlInfo(String datasetSql) {
        Gson gson = new Gson();
        HashMap<String, Object> sqlInfoMap = new HashMap<>();
        String base64SqlStr = Base64.encodeBase64String(datasetSql.getBytes(StandardCharsets.UTF_8));  // 直接获取字符串
        sqlInfoMap.put("sql", base64SqlStr);
        sqlInfoMap.put("isBase64Encryption", true);
        return gson.toJson(sqlInfoMap);
    }

    /**
     * 获取/创建数据集分组信息
     * @param task 任务
     * @return
     */
    private DatasetGroup getDatasetIndexInfo(Top3IndexInfoDTO task) {
        CacheUtils.removeAll(AuthConstants.USER_PERMISSION_CACHE_NAME);
        DatasetGroupExample datasetGroupExample = new DatasetGroupExample();
        datasetGroupExample
                .createCriteria()
                .andNameEqualTo("医院等级评审系统_自动生成")
                .andPidEqualTo("0");
        List<DatasetGroup> topLevelDatasetGroups = datasetGroupMapper.selectByExample(datasetGroupExample);
        //top group name
        DatasetGroup topLevelDatasetGroup = new DatasetGroup();
        if(CollectionUtils.isEmpty(topLevelDatasetGroups)){
            //没有则创建
            topLevelDatasetGroup.setId(UUID.randomUUID().toString());
            topLevelDatasetGroup.setName("医院等级评审系统_自动生成");
            topLevelDatasetGroup.setPid("0");
            topLevelDatasetGroup.setLevel(0);
            topLevelDatasetGroup.setType("group");
            topLevelDatasetGroup.setCreateBy("admin");
            sysAuthService.copyAuth(topLevelDatasetGroup.getId(), SysAuthConstants.AUTH_SOURCE_TYPE_DATASET);
            datasetGroupMapper.insert(topLevelDatasetGroup);
        }else{
            topLevelDatasetGroup = topLevelDatasetGroups.get(0);
        }
        //chapter group name
        DatasetGroupExample datasetGroupSecondExample = new DatasetGroupExample();
        datasetGroupSecondExample
                .createCriteria()
                .andNameEqualTo(task.getChapterName())
                .andPidEqualTo(topLevelDatasetGroup.getId());
        List<DatasetGroup> datasetSecondGroups = datasetGroupMapper.selectByExample(datasetGroupSecondExample);
        //second group name
        DatasetGroup datasetSecondGroup = new DatasetGroup();
        if(CollectionUtils.isEmpty(datasetSecondGroups)){
            //没有则创建
            datasetSecondGroup.setId(UUID.randomUUID().toString());
            datasetSecondGroup.setName(task.getChapterName());
            datasetSecondGroup.setPid(topLevelDatasetGroup.getId());
            datasetSecondGroup.setLevel(1);
            datasetSecondGroup.setType("group");
            datasetSecondGroup.setCreateBy("admin");
            sysAuthService.copyAuth(datasetSecondGroup.getId(), SysAuthConstants.AUTH_SOURCE_TYPE_DATASET);
            datasetGroupMapper.insert(datasetSecondGroup);
        }else{
            datasetSecondGroup = datasetSecondGroups.get(0);
        }

        //index group name
        DatasetGroupExample datasetGroupIndexExample = new DatasetGroupExample();
        datasetGroupIndexExample
                .createCriteria()
                .andNameEqualTo(task.getIndexName())
                .andPidEqualTo(datasetSecondGroup.getId());
        List<DatasetGroup> datasetIndexGroups = datasetGroupMapper.selectByExample(datasetGroupIndexExample);
        //second group name
        DatasetGroup datasetIndexGroup = new DatasetGroup();
        if(CollectionUtils.isEmpty(datasetIndexGroups)){
            //没有则创建
            datasetIndexGroup.setId(UUID.randomUUID().toString());
            datasetIndexGroup.setName(task.getIndexName());
            datasetIndexGroup.setPid(datasetSecondGroup.getId());
            datasetIndexGroup.setLevel(2);
            datasetIndexGroup.setType("group");
            datasetIndexGroup.setCreateBy("admin");
            sysAuthService.copyAuth(datasetIndexGroup.getId(), SysAuthConstants.AUTH_SOURCE_TYPE_DATASET);
            datasetGroupMapper.insert(datasetIndexGroup);
        }else{
            datasetIndexGroup = datasetIndexGroups.get(0);
        }
        return datasetIndexGroup;
    }

    /**
     * 获取数据源
     * @return
     */
    private Datasource getDataSourceInfo() {
        // 读取配置文件中的数据源
        return datasourceMapper.selectByPrimaryKey(datasourceId);
    }

    /**
     * 三甲指标批量生成
     * @param top3IndexBatchTask 三甲指标任务
     * @return
     */
    public Boolean top3IndexBatchGenerate(List<Top3IndexInfoDTO> top3IndexBatchTask) {
        if(CollectionUtils.isEmpty(top3IndexBatchTask)){
            throw new IllegalArgumentException("三甲评审指标生成失败_当前指标明细为空!");
        }
        //设置代理用户
        AuthUtils.setProxyUser(1L);
        //获取数据源
        Datasource datasource = getDataSourceInfo();
        top3IndexBatchTask.forEach(task -> {
            top3IndexGenerate(task,datasource);
        });
        return true;
    }
}
